using System;
using System.Collections;
using System.Linq;
using DG.Tweening;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class WeaponDetails : MonoBehaviour
{
    public Weapons weapon;
    WeaponPrefab weaponPrefab;
    GameObject weaponDetails;
    //public GameObject clickBlock;
    public TextMeshProUGUI weaponName;
    public TextMeshProUGUI weaponDescription;
    public TextMeshProUGUI weaponLevel;
    public TextMeshProUGUI bonusLabel;
    public TextMeshProUGUI atkw;
    public TextMeshProUGUI spdBoost;
    public TextMeshProUGUI shots;
    public TextMeshProUGUI cooldown;
    public TextMeshProUGUI qiMin;
    public TextMeshProUGUI luckMin;
    public TextMeshProUGUI enableLabel;
    public GameObject enabledClassesList;
    public GameObject enabledClassesList2;
    public TextMeshProUGUI targetLabel;
    public GameObject targetClassesList;
    public GameObject targetClassesList2;
    public GameObject slot1, slot2, slot3;
    public GameObject starContainer;
    GameObject star1, star2, star3, star4, star5;
    public GameObject curse;
    public Button bonusContainer;
    public Button qiContainer;
    TextMeshProUGUI qiMinLabel;
    public Button luckContainer;
    TextMeshProUGUI luckMinLabel;
    public Button enabledContainer;
    public Button targetContainer;
    public Button upgradeButton;
    TextMeshProUGUI upgradeButtonText;
    public Button upgradeButtonInactive;
    TextMeshProUGUI upgradeButtonInactiveText;
    public Button improveButton;
    TextMeshProUGUI improveButtonText;
    public Button improveButtonInactive;
    TextMeshProUGUI improveButtonInactiveText;
    public Button atkwContainer;
    public Button shotsBtn;
    public Button cooldownBtn;
    public Button spdBoostContainer;
    Button slo1Btn, slo2Btn, slo3Btn;
    Image background;
    Image outline;
    Image weaponImage;
    Sprite[] weaponSprites;

    Vector3 initialScale;


    // Start is called once before the first execution of Update after the MonoBehaviour is created
    void Awake()
    {
        weaponDetails = gameObject;
        //clickBlock.SetActive(false);

        initialScale = weaponDetails.transform.localScale;

        weaponSprites = new Sprite[]
        {
            Resources.Load<Sprite>("Sprites/Weapons/W1"),
            Resources.Load<Sprite>("Sprites/Weapons/W2"),
            Resources.Load<Sprite>("Sprites/Weapons/W3"),
            Resources.Load<Sprite>("Sprites/Weapons/W4"),
            Resources.Load<Sprite>("Sprites/Weapons/W5")
        };
        outline = weaponDetails.transform.GetChild(0).GetComponent<Image>(); // Set the outline image
        background = weaponDetails.transform.GetChild(1).GetComponent<Image>(); // Set the background image
        weaponImage = weaponDetails.transform.GetChild(4).GetComponent<Image>(); // Set the weapon image
        star1 = starContainer.transform.GetChild(0).gameObject;
        star2 = starContainer.transform.GetChild(1).gameObject;
        star3 = starContainer.transform.GetChild(2).gameObject;
        star4 = starContainer.transform.GetChild(3).gameObject;
        star5 = starContainer.transform.GetChild(4).gameObject;

        slo1Btn = slot1.transform.GetChild(1).GetComponent<Button>();
        slo2Btn = slot2.transform.GetChild(1).GetComponent<Button>();
        slo3Btn = slot3.transform.GetChild(1).GetComponent<Button>();

        upgradeButton.onClick.AddListener(() => // Add the listener to the button to upgrade the weapon
        {
            LevelUpWeapon();
        });

        qiMinLabel = qiContainer.transform.GetChild(1).GetComponent<TextMeshProUGUI>();
        luckMinLabel = luckContainer.transform.GetChild(1).GetComponent<TextMeshProUGUI>();
        upgradeButtonText = upgradeButton.transform.GetChild(0).GetComponent<TextMeshProUGUI>();
        improveButtonText = improveButton.transform.GetChild(0).GetComponent<TextMeshProUGUI>();
        upgradeButtonInactiveText = upgradeButtonInactive.transform.GetChild(0).GetComponent<TextMeshProUGUI>();
        improveButtonInactiveText = improveButtonInactive.transform.GetChild(0).GetComponent<TextMeshProUGUI>();

        //Deactivate every enabledList and targetList child
        for (int i = 0; i < enabledClassesList.transform.childCount; i++)
        {
            enabledClassesList.transform.GetChild(i).gameObject.SetActive(false);
        }
        for (int i = 0; i < enabledClassesList2.transform.childCount; i++)
        {
            enabledClassesList2.transform.GetChild(i).gameObject.SetActive(false);
        }
        for (int i = 0; i < targetClassesList.transform.childCount; i++)
        {
            targetClassesList.transform.GetChild(i).gameObject.SetActive(false);
        }
        for (int i = 0; i < targetClassesList2.transform.childCount; i++)
        {
            targetClassesList2.transform.GetChild(i).gameObject.SetActive(false);
        }

        qiMinLabel.text = KeywordManager.GetWord("WEAPON_QIMIN_TEXT") + ": ";
        luckMinLabel.text = KeywordManager.GetWord("WEAPON_LUCKMIN_TEXT") + ": ";
        bonusLabel.text = KeywordManager.GetWord("WEAPON_PASSIVE_SKILL_TEXT");
        enableLabel.text = KeywordManager.GetWord("WEAPON_COMPATIBLE_TEXT");
        targetLabel.text = KeywordManager.GetWord("WEAPON_VULNERABLE_TEXT");
        upgradeButtonText.text = KeywordManager.GetWord("WEAPON_WLUPGRADE_BUTTON");
        improveButtonText.text = KeywordManager.GetWord("WEAPON_RWIMPROVE_BUTTON");
        upgradeButtonInactiveText.text = KeywordManager.GetWord("WEAPON_WLUPGRADE_BUTTON");
        improveButtonInactiveText.text = KeywordManager.GetWord("WEAPON_RWIMPROVE_BUTTON");

        atkwContainer.onClick.AddListener(() => { InfoBox.Instance.DisplayGeneral(KeywordManager.GetWord("WEAPON_ATKw_EXPLANATION"), atkwContainer.transform); });
        spdBoostContainer.onClick.AddListener(() => { InfoBox.Instance.DisplayGeneral(KeywordManager.GetWord("WEAPON_SPDBoost_EXPLANATION"), spdBoostContainer.transform); });
        weaponImage.GetComponent<Button>().onClick.AddListener(() => { InfoBox.Instance.DisplayGeneral(KeywordManager.GetWord("WEAPON_RARITY_EXPLANATION"), weaponImage.transform); });
        shotsBtn.onClick.AddListener(() => { InfoBox.Instance.DisplayGeneral(KeywordManager.GetWord("WEAPON_SHOT_EXPLANATION"), shotsBtn.transform); });
        cooldownBtn.onClick.AddListener(() => { InfoBox.Instance.DisplayGeneral(KeywordManager.GetWord("WEAPON_COOLDOWN_EXPLANATION"), cooldownBtn.transform); });
        curse.GetComponentInChildren<Button>().onClick.AddListener(() => { InfoBox.Instance.DisplayGeneral(KeywordManager.GetWord("WEAPON_CURSE_EXPLANATION"), curse.transform); });
        starContainer.GetComponent<Button>().onClick.AddListener(() => { InfoBox.Instance.DisplayGeneral(KeywordManager.GetWord("WEAPON_STARS_EXPLANATION"), starContainer.transform); });
        slo1Btn.onClick.AddListener(() => { InfoBox.Instance.DisplayGeneral(KeywordManager.GetWord("WEAPON_SLOTS_EXPLANATION"), slo1Btn.transform); });
        slo2Btn.onClick.AddListener(() => { InfoBox.Instance.DisplayGeneral(KeywordManager.GetWord("WEAPON_SLOTNOTRELEASED_EXPLANATION"), slo2Btn.transform); });
        slo3Btn.onClick.AddListener(() => { InfoBox.Instance.DisplayGeneral(KeywordManager.GetWord("WEAPON_SLOTNOTRELEASED_EXPLANATION"), slo3Btn.transform); });
        weaponLevel.GetComponent<Button>().onClick.AddListener(() => { InfoBox.Instance.DisplayGeneral(KeywordManager.GetWord("WEAPON_LEVEL_EXPLANATION"), weaponLevel.transform); });
        bonusContainer.onClick.AddListener(() => { InfoBox.Instance.DisplayGeneral(KeywordManager.GetWord("WEAPON_PASSIVESKILL_EXPLANATION"), bonusContainer.transform); });
        qiContainer.onClick.AddListener(() => { InfoBox.Instance.DisplayGeneral(KeywordManager.GetWord("WEAPON_QIMIN_EXPLANATION"), qiContainer.transform); });
        luckContainer.onClick.AddListener(() => { InfoBox.Instance.DisplayGeneral(KeywordManager.GetWord("WEAPON_LKMIN_EXPLANATION"), luckContainer.transform); });
        enabledContainer.onClick.AddListener(() => { InfoBox.Instance.DisplayGeneral(KeywordManager.GetWord("WEAPON_COMPATIBLE_EXPLANATION"), enabledContainer.transform); });
        targetContainer.onClick.AddListener(() => { InfoBox.Instance.DisplayGeneral(KeywordManager.GetWord("WEAPON_VULNERABLE_EXPLANATION"), targetContainer.transform); });
        upgradeButtonInactive.onClick.AddListener(() => { InfoBox.Instance.DisplayGeneral(KeywordManager.GetWord("WEAPON_UPGRADE_IMPROVE_ALERT"), upgradeButtonInactive.transform); });
        improveButtonInactive.onClick.AddListener(() => { InfoBox.Instance.DisplayGeneral(KeywordManager.GetWord("WEAPON_ENHANCE_IMPROVE_ALERT"), improveButtonInactive.transform); });

        if (weapon != null) ShowWeaponDetails(weapon);

    }

    // Update is called once per frame
    void Update()
    {
        if (weaponDetails.activeSelf)
        {
            //Rebuild layout every frame
            LayoutRebuilder.ForceRebuildLayoutImmediate(enabledClassesList.GetComponent<RectTransform>());
            LayoutRebuilder.ForceRebuildLayoutImmediate(targetClassesList.GetComponent<RectTransform>());

/*             //if click outside the weapon details, hide it
            if (Input.touchCount > 0 && Input.GetTouch(0).phase == TouchPhase.Began)
            {
                if (!RectTransformUtility.RectangleContainsScreenPoint(weaponDetails.GetComponent<RectTransform>(), Input.GetTouch(0).position, Camera.main))
                {
                    HideWeaponDetails();
                }
            } */
        }


    }


    public void ShowWeaponDetails(Weapons weapon/* , WeaponPrefab weaponPrefab */)
    {

        this.weapon = weapon;
        //this.weaponPrefab = weaponPrefab;

        weaponName.text = weapon.name;
        weaponDescription.text = weapon.description;
        weaponLevel.text = "Lv. " + weapon.wlBase.ToString();
        atkw.text = weapon.atkw.ToString();
        spdBoost.text = weapon.spdBoost.ToString();
        shots.text = weapon.shots.ToString();
        cooldown.text = weapon.cooldown.ToString();
        qiMin.text = weapon.qiMin.ToString();
        luckMin.text = weapon.luckMin.ToString();
        SetRarity();
        SetClasses();

        // If weapon has tg30 tag, show curse image
        if (weapon.weaponTags.Any(x => x.id == "tg30"))
        {
            curse.SetActive(true);
        }
        else
        {
            curse.SetActive(false);
        }

        weaponDetails.transform.localScale = Vector3.zero;

        StartCoroutine(UpdateLayoutAndPositionNextFrame());
        gameObject.transform.SetAsLastSibling();
        //clickBlock.SetActive(true);
        weaponDetails.SetActive(true);

    }


    void SetRarity()
    {
        if (weapon.wlBase >= 1 && weapon.wlBase <= 6)
        {
            background.color = GeneralInfo.GetTierColor("TR2");
            outline.color = GeneralInfo.GetTierColor("TR2");
            weaponImage.sprite = weaponSprites[0];
            starContainer.SetActive(true);
            star1.SetActive(true);
            star2.SetActive(true);
            star3.SetActive(false);
            star4.SetActive(false);
            star5.SetActive(false);
        }
        else if (weapon.wlBase >= 7 && weapon.wlBase <= 12)
        {
            background.color = GeneralInfo.GetTierColor("TR3");
            outline.color = GeneralInfo.GetTierColor("TR3");
            weaponImage.sprite = weaponSprites[1];
            starContainer.SetActive(true);
            star1.SetActive(true);
            star2.SetActive(true);
            star3.SetActive(true);
            star4.SetActive(false);
            star5.SetActive(false);
        }
        else if (weapon.wlBase >= 13 && weapon.wlBase <= 16)
        {
            background.color = GeneralInfo.GetTierColor("TR1");
            outline.color = GeneralInfo.GetTierColor("TR1");
            weaponImage.sprite = weaponSprites[2];
            starContainer.SetActive(true);
            star1.SetActive(true);
            star2.SetActive(true);
            star3.SetActive(true);
            star4.SetActive(true);
            star5.SetActive(false);
        }
        else if (weapon.wlBase >= 17 && weapon.wlBase <= 20)
        {
            background.color = GeneralInfo.GetTierColor("TR6");
            outline.color = GeneralInfo.GetTierColor("TR6");
            weaponImage.sprite = weaponSprites[3];
            starContainer.SetActive(true);
            star1.SetActive(true);
            star2.SetActive(true);
            star3.SetActive(true);
            star4.SetActive(true);
            star5.SetActive(true);
        }
        else
        {
            background.color = Color.black;
            outline.color = Color.black;
            weaponImage.sprite = weaponSprites[4];
            starContainer.SetActive(false);
        }

        if (weapon.wlBase < 20)
        {
            upgradeButton.gameObject.SetActive(true);
            upgradeButtonInactive.gameObject.SetActive(false);
            improveButton.gameObject.SetActive(false);
            improveButtonInactive.gameObject.SetActive(true);
        }
        else
        {
            upgradeButton.gameObject.SetActive(false);
            upgradeButtonInactive.gameObject.SetActive(true);
            improveButton.gameObject.SetActive(true);
            improveButtonInactive.gameObject.SetActive(false);
        }
    }


    void SetClasses()
    {
        //Deactivate every enabledList and targetList child
        for (int i = 0; i < enabledClassesList.transform.childCount; i++)
        {
            enabledClassesList.transform.GetChild(i).gameObject.SetActive(false);
        }
        for (int i = 0; i < enabledClassesList2.transform.childCount; i++)
        {
            enabledClassesList2.transform.GetChild(i).gameObject.SetActive(false);
        }
        for (int i = 0; i < targetClassesList.transform.childCount; i++)
        {
            targetClassesList.transform.GetChild(i).gameObject.SetActive(false);
        }
        for (int i = 0; i < targetClassesList2.transform.childCount; i++)
        {
            targetClassesList2.transform.GetChild(i).gameObject.SetActive(false);
        }

        for (int i = 0; i < weapon.enabledClasses.Length; i++)
        {
            // Populate enabled classes list 2 if the first one is full
            if (i >= enabledClassesList.transform.childCount)
            {
                enabledClassesList2.transform.GetChild(i - enabledClassesList.transform.childCount).gameObject.SetActive(true);
                enabledClassesList2.transform.GetChild(i - enabledClassesList.transform.childCount).GetChild(0).GetComponent<TextMeshProUGUI>().text = GeneralInfo.GetClassName(weapon.enabledClasses[i].id);
                continue;
            }
            enabledClassesList.transform.GetChild(i).gameObject.SetActive(true);
            enabledClassesList.transform.GetChild(i).GetChild(0).GetComponent<TextMeshProUGUI>().text = GeneralInfo.GetClassName(weapon.enabledClasses[i].id);

        }

        for (int i = 0; i < weapon.targetClasses.Length; i++)
        {
            // Populate target classes list 2 if the first one is full
            if (i >= targetClassesList.transform.childCount)
            {
                targetClassesList2.transform.GetChild(i - targetClassesList.transform.childCount).gameObject.SetActive(true);
                targetClassesList2.transform.GetChild(i - targetClassesList.transform.childCount).GetChild(0).GetComponent<TextMeshProUGUI>().text = GeneralInfo.GetClassName(weapon.targetClasses[i].id);
                continue;
            }
            targetClassesList.transform.GetChild(i).gameObject.SetActive(true);
            targetClassesList.transform.GetChild(i).GetChild(0).GetComponent<TextMeshProUGUI>().text = GeneralInfo.GetClassName(weapon.targetClasses[i].id);
        }

        StartCoroutine(UpdateLayoutAndPositionNextFrame());
    }

    private IEnumerator UpdateLayoutAndPositionNextFrame()
    {
        yield return null; // wait 1 frame

        LayoutRebuilder.ForceRebuildLayoutImmediate(enabledClassesList.GetComponent<RectTransform>());
        LayoutRebuilder.ForceRebuildLayoutImmediate(targetClassesList.GetComponent<RectTransform>());

        weaponDetails.transform.DOScale(initialScale, 0.2f).SetEase(Ease.OutBack);

    }

    public void HideWeaponDetails()
    {
        weaponDetails.transform.DOScale(Vector3.zero, 0.2f).SetEase(Ease.InBack).OnComplete(() =>
        {
            weaponDetails.SetActive(false);
            //clickBlock.SetActive(false);
        });
    }

    void LevelUpWeapon()
    {
        weapon.wlBase = Mathf.Min(weapon.wlBase + 1, 20);
        if (weapon.wlBase == 20)
        {
            upgradeButton.gameObject.SetActive(false);
            upgradeButtonInactive.gameObject.SetActive(true);
            improveButtonInactive.gameObject.SetActive(false);
            improveButton.gameObject.SetActive(true);
        }
        weapon.SetWeapon(weapon.wlBase);
        SetRarity();
        atkw.text = weapon.atkw.ToString();
        shots.text = weapon.shots.ToString();
        weaponLevel.text = "Lv. " + weapon.wlBase.ToString();

        //weaponPrefab.RefreshUI();
    }

}
