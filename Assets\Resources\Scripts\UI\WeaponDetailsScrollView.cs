using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class WeaponDetailsScrollView : MonoBehaviour
{
    public WeaponsSelectorSrollView weaponSelector;
    public GameObject clickBlock;
    public Transform content;
    public ScrollRect scrollRect;
    public GameObject weaponDetailsPrefab;

    // POOL
    private Queue<GameObject> weaponDetailsPool = new Queue<GameObject>();
    private readonly List<GameObject> activeWeaponDetails = new List<GameObject>();
    private const int PoolSize = 45;

    // Start is called once before the first execution of Update after the MonoBehaviour is created
    void Start()
    {
        // Pre-instantiate 45 weapon details and add to pool
        for (int i = 0; i < PoolSize; i++)
        {
            GameObject obj = Instantiate(weaponDetailsPrefab, content);
            obj.SetActive(false);
            weaponDetailsPool.Enqueue(obj);
        }
    }

    // Update is called once per frame
    void Update()
    {
        if (Input.touchCount > 0 && Input.GetTouch(0).phase == TouchPhase.Began)
        {
            Vector2 touchPos = Input.GetTouch(0).position;

            bool touchedAChild = false;

            foreach (Transform child in content)
            {
                RectTransform childRect = child as RectTransform;
                if (childRect == null) continue;

                if (RectTransformUtility.RectangleContainsScreenPoint(childRect, touchPos, Camera.main))
                {
                    touchedAChild = true;
                    break;
                }
            }

            if (!touchedAChild)
            {
                gameObject.SetActive(false);
                ReturnAllToPool(); // Return to pool instead of destroying
                if (clickBlock != null)
                    clickBlock.SetActive(false);
            }
        }
    }

    void OnEnable()
    {
        LoadWeaponsDetails();
    }

void LoadWeaponsDetails()
{
    ReturnAllToPool(); // Return currently active objects to the pool

    int weaponCount = weaponSelector.weapons.Count;

    // Dynamically grow the pool if needed
    int additionalNeeded = weaponCount - weaponDetailsPool.Count;
    if (additionalNeeded > 0)
    {
        Debug.LogWarning($"Pool too small. Instantiating {additionalNeeded} additional weapon detail objects.");
        for (int i = 0; i < additionalNeeded; i++)
        {
            GameObject obj = Instantiate(weaponDetailsPrefab, content);
            obj.SetActive(false);
            weaponDetailsPool.Enqueue(obj);
        }
    }

    for (int i = 0; i < weaponCount; i++)
    {
        Weapons weapon = weaponSelector.weapons[i];

        GameObject weaponDetails = weaponDetailsPool.Dequeue();
        weaponDetails.transform.SetAsLastSibling(); // Keep layout order
        weaponDetails.SetActive(true);

        var detailsComponent = weaponDetails.GetComponent<WeaponDetails>();
        detailsComponent.weapon = weapon;
        detailsComponent.ShowWeaponDetails(weapon);

        activeWeaponDetails.Add(weaponDetails);
    }

    Debug.Log($"Displayed {activeWeaponDetails.Count} weapon details.");
}
    GameObject GetPooledWeaponDetails()
    {
        if (weaponDetailsPool.Count > 0)
        {
            return weaponDetailsPool.Dequeue();
        }
        else
        {
            return Instantiate(weaponDetailsPrefab);
        }
    }

    void ReturnAllToPool()
    {
        foreach (Transform child in content)
        {
            child.gameObject.SetActive(false);
            weaponDetailsPool.Enqueue(child.gameObject);
        }
    }
}
