using System.Collections;
using System.Linq;
using DG.Tweening;
using TMPro;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

public class WeaponPrefab : MonoBehaviour
{
    ConfigsHandler configsHandler;
    WeaponsSelectorSrollView weaponSelector;
    WeaponDetails weaponDetails;
    WeaponActiveSV weaponActiveSV;
    Canvas canvas;
    public Weapons weapon;
    Image background;
    Image outline;
    Image weaponImage;
    TextMeshProUGUI atkw;
    TextMeshProUGUI lvl;
    GameObject starContainer;
    GameObject star1, star2, star3, star4, star5;
    GameObject curse;
    TextMeshProUGUI shots;
    Sprite[] weaponSprites;
    GameObject weaponIcon;

    //Selected
    GameObject selected;
    Button equipButton;
    TextMeshProUGUI equipButtonText;
    Button removeButton;
    TextMeshProUGUI removeButtonText;
    Button detailsButton;
    TextMeshProUGUI detailsButtonText;

    // Start is called once before the first execution of Update after the MonoBehaviour is created
    void Start()
    {
        configsHandler = GameObject.Find("GameConfigsHandler").GetComponent<ConfigsHandler>();
        weaponSelector = GameObject.Find("WeaponsSelectorSrollView").GetComponent<WeaponsSelectorSrollView>();
        weaponDetails = GameObject.Find("WeaponDet").GetComponent<WeaponDetails>();
        canvas = GameObject.Find("Canvas").GetComponent<Canvas>();
        weaponActiveSV = GameObject.Find("WeaponDet").GetComponent<WeaponActiveSV>();

        weaponSprites = new Sprite[]
        {
            Resources.Load<Sprite>("Sprites/Weapons/W1"),
            Resources.Load<Sprite>("Sprites/Weapons/W2"),
            Resources.Load<Sprite>("Sprites/Weapons/W3"),
            Resources.Load<Sprite>("Sprites/Weapons/W4"),
            Resources.Load<Sprite>("Sprites/Weapons/W5")
        };

        //Selected
        selected = transform.GetChild(0).gameObject;
        selected.SetActive(false);
        equipButton = selected.transform.GetChild(1).GetComponent<Button>(); // Set the equip button
        equipButtonText = equipButton.transform.GetChild(0).GetComponent<TextMeshProUGUI>(); // Set the equip button text
        removeButton = selected.transform.GetChild(2).GetComponent<Button>(); // Set the remove button
        removeButtonText = removeButton.transform.GetChild(0).GetComponent<TextMeshProUGUI>(); // Set the remove button text
        detailsButton = selected.transform.GetChild(3).GetComponent<Button>(); // Set the details button
        detailsButtonText = detailsButton.transform.GetChild(0).GetComponent<TextMeshProUGUI>(); // Set the details button text

        background = transform.GetChild(2).GetComponent<Image>(); // Set the background image
        outline = transform.GetChild(3).GetComponent<Image>(); // Set the outline image
        weaponImage = transform.GetChild(4).GetComponent<Image>(); // Set the weapon image
        atkw = transform.GetChild(5).GetChild(1).GetComponent<TextMeshProUGUI>(); // Set the weapon attack
        lvl = transform.GetChild(6).GetComponent<TextMeshProUGUI>(); // Set the weapon level
        curse = transform.GetChild(8).gameObject; // Set the curse image
        shots = transform.GetChild(9).GetChild(0).GetChild(0).GetComponent<TextMeshProUGUI>(); // Set the weapon shots

        starContainer = transform.GetChild(7).gameObject; // Set the star container, number of stars related to the weapon rarity
        star1 = starContainer.transform.GetChild(0).gameObject;
        star2 = starContainer.transform.GetChild(1).gameObject;
        star3 = starContainer.transform.GetChild(2).gameObject;
        star4 = starContainer.transform.GetChild(3).gameObject;
        star5 = starContainer.transform.GetChild(4).gameObject;
        starContainer.SetActive(false);

        atkw.text = weapon.atkw.ToString();

        // If weapon has tg30 tag, show curse image
        if (weapon.weaponTags.Any(x => x.id == "tg30"))
        {
            curse.SetActive(true);
        }
        else
        {
            curse.SetActive(false);
        }
        shots.text = weapon.shots.ToString();
        lvl.text = "Lv. " + weapon.wlBase.ToString();

        SetRarity();

        equipButtonText.text = KeywordManager.GetWord("WEAPON_EQUIP_BUTTON");
        removeButtonText.text = KeywordManager.GetWord("WEAPON_REMOVE_BUTTON");
        detailsButtonText.text = KeywordManager.GetWord("WEAPON_DETAILS_BUTTON");

        //Listeners
        gameObject.GetComponent<Button>().onClick.AddListener(() => // Add the listener to the button to equip the weapon
        {
            SelectWeapon();
        });
        equipButton.onClick.AddListener(() => // Add the listener to the button to equip the weapon
        {
            EquipWeapon();
        });
        removeButton.onClick.AddListener(() => // Add the listener to the button to remove the weapon
        {
            RemoveWeapon(weaponSelector.selectedCharacter, weapon);
        });
        detailsButton.onClick.AddListener(() => // Add the listener to the button to show the weapon details
        {
            weaponActiveSV.ActivateWeaponDetailsScroll();
            //weaponDetails.ShowWeaponDetails(weapon, this);
        });



    }

    // Update is called once per frame
    void Update()
    {


        //SetRarity();

    }

    void EquipWeapon() // Equips the weapon to the character
    {
        // Cant equip the same weapon twice
        if (weaponSelector.selectedCharacter.weaponsQuantity > 0 && weaponSelector.selectedCharacter.weaponHeld == weapon)
        {
            //play sound effect
            ConfMenu.PlaySoundWithVolume(GameObject.Find("GameConfigsHandler").GetComponent<AudioSource>(), Resources.Load<AudioClip>("SFX/RejectedSelect"));
            return;
        }

        if (weapon.isBeingUsed)
        {
            //Search for the character holding the weapon and remove it
            BattleCharacter[] characters = configsHandler.GetActiveCharacters();
            foreach (var character in characters)
            {
                if (character.weaponHeld == weapon)
                {
                    RemoveWeapon(character, weapon);
                }
            }
        }

        if (weaponSelector.selectedCharacter.weaponHeld != null)
        {
            // If the character already has a weapon, remove it
            Weapons selectedCharacterWeapon = weaponSelector.selectedCharacter.weaponHeld;
            RemoveWeapon(weaponSelector.selectedCharacter, selectedCharacterWeapon);
        }



        //play sound effect
        ConfMenu.PlaySoundWithVolume(GameObject.Find("GameConfigsHandler").GetComponent<AudioSource>(), Resources.Load<AudioClip>("SFX/MenuSound"));

        weaponSelector.selectedCharacter.weaponHeld = weapon; // Sets the weapon to the character
        weaponSelector.selectedCharacter.weaponsQuantity++; // Increments the weapons quantity

        GameObject iconPrefab = Resources.Load<GameObject>("Prefabs/weaponIcon"); // Load the weapon icon prefab

        weaponIcon = Instantiate(iconPrefab, canvas.transform); // Instantiate the weapon icon prefab
        weaponIcon.GetComponent<WeaponIcon>().weapon = weapon; // Sets the weapon to the weapon icon
        weaponIcon.GetComponent<WeaponIcon>().SetIcon(weaponImage.sprite, background.color, weapon); // Sets the weapon icon

        weapon.isBeingUsed = true; // Sets the weapon as being used

        //If is being used, dispay remove button, else display equip button
        if (weapon.isBeingUsed)
        {
            removeButton.gameObject.SetActive(true);
            equipButton.gameObject.SetActive(false);
        }
        else
        {
            removeButton.gameObject.SetActive(false);
            equipButton.gameObject.SetActive(true);
        }

        //Refresh all weapon prefabs
        weaponSelector.RefreshAllUI();
    }

    void RemoveWeapon(BattleCharacter character, Weapons weaponTORemove)
    {
        //play sound effect
        ConfMenu.PlaySoundWithVolume(GameObject.Find("GameConfigsHandler").GetComponent<AudioSource>(), Resources.Load<AudioClip>("SFX/MenuSound"));

        weaponTORemove.isBeingUsed = false; // Sets the weapon as not being used
        gameObject.GetComponent<Button>().interactable = true; // Enables the button
        character.weaponHeld = null; // Removes the weapon from the character
        // Never lets quantity go below 0
        character.weaponsQuantity = Mathf.Max(0, weaponSelector.selectedCharacter.weaponsQuantity - 1);


        //If is being used, dispay remove button, else display equip button
        if (weaponTORemove.isBeingUsed)
        {
            removeButton.gameObject.SetActive(true);
            equipButton.gameObject.SetActive(false);
        }
        else
        {
            removeButton.gameObject.SetActive(false);
            equipButton.gameObject.SetActive(true);
        }

        //Refresh all weapon prefabs
        weaponSelector.RefreshAllUI();

        WeaponIcon[] allIcons = FindObjectsByType<WeaponIcon>(FindObjectsSortMode.None);
        foreach (var icon in allIcons)
        {
            if (icon.weapon == weaponTORemove)
            {
                Destroy(icon.gameObject);
                break; // optional, if only one icon per weapon
            }
        }

    }
    void SelectWeapon()
    {
        //play sound effect
        ConfMenu.PlaySoundWithVolume(GameObject.Find("GameConfigsHandler").GetComponent<AudioSource>(), Resources.Load<AudioClip>("SFX/MenuSound"));

        //Deactivate every other selected
        WeaponPrefab[] allWeapons = FindObjectsByType<WeaponPrefab>(FindObjectsSortMode.None);
        foreach (var weapon in allWeapons)
        {
            if (weapon != this)
            {
                weapon.selected.SetActive(false);
            }
        }

        //If is being used, dispay remove button, else display equip button
        if (weapon.isBeingUsed && weaponSelector.selectedCharacter.weaponHeld == weapon)
        {
            removeButton.gameObject.SetActive(true);
            equipButton.gameObject.SetActive(false);
        }
        else
        {
            removeButton.gameObject.SetActive(false);
            equipButton.gameObject.SetActive(true);
        }

        //Activate this selected
        if (selected.activeSelf)
        {
            selected.SetActive(false);
            weaponSelector.selectedWeapon = null;
        }
        else
        {
            selected.SetActive(true);
            //PlaySelectBounce();
            weaponSelector.selectedWeapon = weapon;
        }
    }

    void LevelUpWeapon()
    {
        weapon.wlBase++;
        if (weapon.wlBase > 20) return;
        weapon.SetWeapon(weapon.wlBase);
        atkw.text = weapon.atkw.ToString();
        shots.text = weapon.shots.ToString();
        lvl.text = "Lv. " + weapon.wlBase.ToString();
    }

    void SetRarity()
    {
        if (weapon.wlBase >= 1 && weapon.wlBase <= 6)
        {
            background.color = GeneralInfo.GetTierColor("TR2");
            outline.color = GeneralInfo.GetTierColor("TR2");
            weaponImage.sprite = weaponSprites[0];
            starContainer.SetActive(true);
            star1.SetActive(true);
            star2.SetActive(true);
            star3.SetActive(false);
            star4.SetActive(false);
            star5.SetActive(false);
        }
        else if (weapon.wlBase >= 7 && weapon.wlBase <= 12)
        {
            background.color = GeneralInfo.GetTierColor("TR3");
            outline.color = GeneralInfo.GetTierColor("TR3");
            weaponImage.sprite = weaponSprites[1];
            starContainer.SetActive(true);
            star1.SetActive(true);
            star2.SetActive(true);
            star3.SetActive(true);
            star4.SetActive(false);
            star5.SetActive(false);
        }
        else if (weapon.wlBase >= 13 && weapon.wlBase <= 16)
        {
            background.color = GeneralInfo.GetTierColor("TR1");
            outline.color = GeneralInfo.GetTierColor("TR1");
            weaponImage.sprite = weaponSprites[2];
            starContainer.SetActive(true);
            star1.SetActive(true);
            star2.SetActive(true);
            star3.SetActive(true);
            star4.SetActive(true);
            star5.SetActive(false);
        }
        else if (weapon.wlBase >= 17 && weapon.wlBase <= 20)
        {
            background.color = GeneralInfo.GetTierColor("TR6");
            outline.color = GeneralInfo.GetTierColor("TR6");
            weaponImage.sprite = weaponSprites[3];
            starContainer.SetActive(true);
            star1.SetActive(true);
            star2.SetActive(true);
            star3.SetActive(true);
            star4.SetActive(true);
            star5.SetActive(true);
        }
        else
        {
            background.color = Color.black;
            outline.color = Color.black;
            weaponImage.sprite = weaponSprites[4];
            starContainer.SetActive(false);
        }
    }

    public void RefreshUI()
    {
        if (weapon == null || atkw == null || shots == null || lvl == null) return;

        atkw.text = weapon.atkw.ToString();
        shots.text = weapon.shots.ToString();

        // If weapon has tg30 tag, show curse image
        if (weapon.weaponTags.Any(x => x.id == "tg30"))
        {
            curse.SetActive(true);
        }
        else
        {
            curse.SetActive(false);
        }
        lvl.text = "Lv. " + weapon.wlBase.ToString();


        SetRarity();

        //If is selected dispay selected overlay
        if (weaponSelector.selectedWeapon == weapon)
        {
            selected.SetActive(true);
        }
        else
        {
            selected.SetActive(false);
        }

        //If is being used, dispay remove button, else display equip button
        if (weapon.isBeingUsed && weaponSelector.selectedCharacter.weaponHeld == weapon)
        {
            removeButton.gameObject.SetActive(true);
            equipButton.gameObject.SetActive(false);
        }
        else
        {
            removeButton.gameObject.SetActive(false);
            equipButton.gameObject.SetActive(true);
        }
    }

    public void CleanupForPooling()
    {
        weapon = null;
        atkw.text = "";
        shots.text = "";
        lvl.text = "";
        starContainer.SetActive(false);
        selected.SetActive(false);
        gameObject.GetComponent<Button>().interactable = true;
    }

        private void PlaySelectBounce()
    {

        // Reset any existing tweens on this transform to avoid stacking
        DOTween.Kill("Bounce");

        // Start from default scale
        Vector3 initialScale = gameObject.transform.localScale;
        //cardPrefab.transform.localScale = Vector3.one;

        // Apply bounce animation
        gameObject.transform
            .DOScale(initialScale * 1.1f, 0.15f) // scale up slightly
            .SetEase(Ease.OutQuad)
            .SetId("Bounce")
            .OnComplete(() =>
            {
                gameObject.transform
                    .DOScale(initialScale, 0.15f) // scale back to normal
                    .SetEase(Ease.InQuad);
            });
    }
}
