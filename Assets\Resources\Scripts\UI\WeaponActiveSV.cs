using System.Collections.Generic;
using UnityEngine;

public class WeaponActiveSV : MonoBehaviour
{
    public GameObject weaponDetailsScroll;
    public GameObject clickBlock;
    // Start is called once before the first execution of Update after the MonoBehaviour is created
    void Start()
    {

    }

    // Update is called once per frame
    void Update()
    {
        if (Input.touchCount > 0 && Input.GetTouch(0).phase == TouchPhase.Began)
        {
            foreach (var child in weaponDetailsScroll.GetComponentsInChildren<RectTransform>())
            {
                if (RectTransformUtility.RectangleContainsScreenPoint(child, Input.GetTouch(0).position, Camera.main))
                {
                    return;
                }
            }

            weaponDetailsScroll.SetActive(false);
            clickBlock.SetActive(false);
        }
    }

    public void ActivateWeaponDetailsScroll()
    {
        weaponDetailsScroll.SetActive(true);
        clickBlock.SetActive(true);
    }
}
